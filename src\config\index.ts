/**
 * 配置文件合并机制
 * 根据环境变量或当前语言设置加载对应的语言配置文件
 * 并与通用配置文件进行合并
 */

import commonConfig from './config'
import cnConfig from './config.cn'
import enConfig from './config.en'

/**
 * 深度合并对象
 */
function deepMerge(target: any, source: any): any {
  const result = { ...target }
  
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (
        typeof source[key] === 'object' && 
        source[key] !== null && 
        !Array.isArray(source[key]) &&
        typeof target[key] === 'object' && 
        target[key] !== null && 
        !Array.isArray(target[key])
      ) {
        result[key] = deepMerge(target[key], source[key])
      } else {
        result[key] = source[key]
      }
    }
  }
  
  return result
}

/**
 * 获取当前语言设置
 * 优先级：localStorage > 配置文件 > 浏览器语言 > 默认语言
 */
function getCurrentLanguage(): string {
  // 1. 检查localStorage（优先级最高）
  const storedLang = localStorage.getItem('locale')
  if (storedLang) {
    return storedLang === 'zh-CN' ? 'cn' : 'en'
  }

  // 2. 检查配置文件中的语言设置
  const configLang = (import.meta as any).env?.VITE_LANGUAGE
  if (configLang) {
    return configLang
  }

  // 3. 检查浏览器语言
  const browserLang = navigator.language
  if (browserLang) {
    if (browserLang.startsWith('zh')) {
      return 'cn'
    } else if (browserLang.startsWith('en')) {
      return 'en'
    }
  }

  // 4. 默认语言
  return 'cn'
}

/**
 * 根据语言代码获取对应的配置文件
 */
function getLanguageConfig(language: string) {
  switch (language) {
    case 'cn':
    case 'zh':
    case 'zh-CN':
      return cnConfig
    case 'en':
    case 'en-US':
      return enConfig
    default:
      console.warn(`Unsupported language: ${language}, fallback to Chinese`)
      return cnConfig
  }
}

/**
 * 创建合并后的配置对象
 */
function createConfig(language: string | null = null) {
  const currentLang = language || getCurrentLanguage()
  const languageConfig = getLanguageConfig(currentLang)
  
  // 合并通用配置和语言特定配置
  const mergedConfig = deepMerge(commonConfig, languageConfig)
  
  // 添加元信息
  mergedConfig._meta = {
    language: currentLang,
    timestamp: Date.now(),
  }
  
  return mergedConfig
}

/**
 * 获取配置项的值
 */
function getConfigValue(path: string, defaultValue: any = null, config: any = null) {
  const targetConfig = config || globalConfig
  const keys = path.split('.')
  let value = targetConfig
  
  for (const key of keys) {
    if (value && typeof value === 'object' && key in value) {
      value = value[key]
    } else {
      return defaultValue
    }
  }
  
  return value
}

/**
 * 重新加载配置（当语言切换时调用）
 */
function reloadConfig(newLanguage: string) {
  const newConfig = createConfig(newLanguage)
  
  // 更新全局配置
  Object.keys(globalConfig).forEach(key => {
    delete globalConfig[key]
  })
  Object.assign(globalConfig, newConfig)
  
  console.log(`Configuration reloaded for language: ${newLanguage}`)
}

// 创建全局配置实例
const globalConfig = createConfig()

// 导出配置对象和工具函数
export default globalConfig

export {
  createConfig,
  getConfigValue,
  reloadConfig,
  getCurrentLanguage,
  deepMerge
}
